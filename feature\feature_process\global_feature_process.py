#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2024 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


class GlobalFeatureProcess:
    def __init__(self, camp):
        self.camp = camp

    def reset(self, camp):
        self.camp = camp

    def process_global_feature(self, frame_state):
        """處理全局特徵"""
        global_feature = []
        
        # 遊戲進度時間特徵: frameNo / 20000
        frame_no = frame_state.get("frameNo", 0)
        time_progress = frame_no / 20000.0
        global_feature.append(time_progress)
        
        # 死亡事件特徵
        death_features = self.process_death_events(frame_state)
        global_feature.extend(death_features)
        
        return global_feature
        
    def process_death_events(self, frame_state):
        """處理死亡事件特徵 - 使用one hot編碼"""
        death_features = []
        
        # 獲取死亡事件列表
        frame_action = frame_state.get('frame_action', {})
        dead_actions = frame_action.get('dead_action', [])
        
        # 最多處理最近3個死亡事件，每個事件用one hot編碼
        max_death_events = 3
        
        # 為每個死亡事件創建one hot編碼
        # 編碼格式：[我方擊殺敵方英雄, 敵方擊殺我方英雄, 我方擊殺敵方小兵, 敵方擊殺我方小兵, 我方擊殺敵方塔, 敵方擊殺我方塔, 我方擊殺野怪, 敵方擊殺野怪]
        for i in range(max_death_events):
            if i < len(dead_actions):
                death_event = dead_actions[i]
                death = death_event.get('death', {})
                killer = death_event.get('killer', {})
                
                # 獲取死亡單位信息
                death_sub_type = death.get('sub_type', '')
                death_camp = death.get('camp', '')
                
                # 獲取擊殺者信息
                killer_camp = killer.get('camp', '')
                
                # 判斷死亡單位類型
                if death_sub_type == "ACTOR_SUB_HERO":
                    death_type = 'hero'
                elif death_sub_type == "ACTOR_SUB_SOLDIER":
                    death_type = 'soldier'
                elif death_sub_type == "ACTOR_SUB_TOWER":
                    death_type = 'tower'
                elif death_sub_type == "ACTOR_SUB_MONSTER":
                    death_type = 'monster'
                else:
                    death_type = 'other'
                
                # 判斷陣營關係
                is_our_camp = death_camp == self.camp
                is_enemy_camp = death_camp != self.camp and death_camp != ''
                is_our_killer = killer_camp == self.camp
                is_enemy_killer = killer_camp != self.camp and killer_camp != ''
                
                # 創建one hot編碼
                death_one_hot = [0, 0, 0, 0, 0, 0, 0, 0]  # 8個類別
                
                if death_type == 'hero':
                    if is_our_camp and is_enemy_killer:
                        death_one_hot[1] = 1  # 敵方擊殺我方英雄
                    elif is_enemy_camp and is_our_killer:
                        death_one_hot[0] = 1  # 我方擊殺敵方英雄
                elif death_type == 'soldier':
                    if is_our_camp and is_enemy_killer:
                        death_one_hot[3] = 1  # 敵方擊殺我方小兵
                    elif is_enemy_camp and is_our_killer:
                        death_one_hot[2] = 1  # 我方擊殺敵方小兵
                elif death_type == 'tower':
                    if is_our_camp and is_enemy_killer:
                        death_one_hot[5] = 1  # 敵方擊殺我方塔
                    elif is_enemy_camp and is_our_killer:
                        death_one_hot[4] = 1  # 我方擊殺敵方塔
                elif death_type == 'monster':
                    if is_our_killer:
                        death_one_hot[6] = 1  # 我方擊殺野怪
                    elif is_enemy_killer:
                        death_one_hot[7] = 1  # 敵方擊殺野怪
                
                death_features.extend(death_one_hot)
            else:
                # 如果沒有死亡事件，用0填充
                death_features.extend([0, 0, 0, 0, 0, 0, 0, 0])
        
        return death_features
