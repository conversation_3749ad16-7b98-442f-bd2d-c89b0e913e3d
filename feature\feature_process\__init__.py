#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2024 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""

from agent_ppo.feature.feature_process.hero_process import HeroProcess
from agent_ppo.feature.feature_process.organ_process import OrganProcess
from agent_ppo.feature.feature_process.soldier_process import SoldierProcess
from agent_ppo.feature.feature_process.global_feature_process import GlobalFeatureProcess

class FeatureProcess:
    def __init__(self, camp):
        self.camp = camp
        self.hero_process = HeroProcess(camp)
        self.organ_process = OrganProcess(camp)
        self.soldier_process = SoldierProcess(camp)
        self.global_feature_process = GlobalFeatureProcess(camp)

    def reset(self, camp):
        self.camp = camp
        self.hero_process = HeroProcess(camp)
        self.organ_process = OrganProcess(camp)
        self.soldier_process = SoldierProcess(camp)
        self.global_feature_process = GlobalFeatureProcess(camp)

    def process_organ_feature(self, frame_state):
        return self.organ_process.process_vec_organ(frame_state)

    def process_hero_feature(self, frame_state):
        return self.hero_process.process_vec_hero(frame_state)

    def process_soldier_feature(self, frame_state):
        return self.soldier_process.process_vec_soldier(frame_state)

    def process_feature(self, observation):
        frame_state = observation["frame_state"]
        #藍方小兵
        #{'config_id': 6800, 'runtime_id': 38, 'actor_type': 'ACTOR_MONSTER', 'sub_type': 'ACTOR_SUB_SOLDIER', 'camp': 'PLAYERCAMP_1', 'behav_mode': 'Attack_Path', 'location': {'x': -25758, 'y': 48, 'z': -27667}, 'forward': {'x': -707, 'y': 0, 'z': 707}, 'hp': 1488, 'max_hp': 1488, 'values': {'phy_atk': 60, 'phy_def': 180, 'mgc_atk': 60, 'mgc_def': 0, 'mov_spd': 3088, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 750, ...}, 'abilities': [False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, ...], 'attack_range': 1500, 'attack_target': 0, 'kill_income': 75, 'camp_visible': [True, False], 'sight_area': 5500, 'buff_state': {}}
        #{'config_id': 6801, 'runtime_id': 41, 'actor_type': 'ACTOR_MONSTER', 'sub_type': 'ACTOR_SUB_SOLDIER', 'camp': 'PLAYERCAMP_1', 'behav_mode': 'Attack_Path', 'location': {'x': -25758, 'y': 48, 'z': -27667}, 'forward': {'x': -707, 'y': 0, 'z': 707}, 'hp': 1356, 'max_hp': 1356, 'values': {'phy_atk': 90, 'phy_def': 180, 'mgc_atk': 90, 'mgc_def': 0, 'mov_spd': 3088, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 750, ...}, 'abilities': [False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, ...], 'attack_range': 7000, 'attack_target': 0, 'kill_income': 46, 'camp_visible': [True, False], 'sight_area': 8500, 'buff_state': {}}
        #紅方小兵
        #{'config_id': 6803, 'runtime_id': 39, 'actor_type': 'ACTOR_MONSTER', 'sub_type': 'ACTOR_SUB_SOLDIER', 'camp': 'PLAYERCAMP_2', 'behav_mode': 'Attack_Path', 'location': {'x': 24487, 'y': 48, 'z': 27683}, 'forward': {'x': -707, 'y': 0, 'z': 707}, 'hp': 1488, 'max_hp': 1488, 'values': {'phy_atk': 60, 'phy_def': 180, 'mgc_atk': 60, 'mgc_def': 0, 'mov_spd': 3088, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 750, ...}, 'abilities': [False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, ...], 'attack_range': 1500, 'attack_target': 0, 'kill_income': 75, 'camp_visible': [False, True], 'sight_area': 5500, 'buff_state': {}}
        #{'config_id': 6804, 'runtime_id': 43, 'actor_type': 'ACTOR_MONSTER', 'sub_type': 'ACTOR_SUB_SOLDIER', 'camp': 'PLAYERCAMP_2', 'behav_mode': 'Attack_Path', 'location': {'x': 24487, 'y': 48, 'z': 27683}, 'forward': {'x': -707, 'y': 0, 'z': 707}, 'hp': 1356, 'max_hp': 1356, 'values': {'phy_atk': 90, 'phy_def': 180, 'mgc_atk': 90, 'mgc_def': 0, 'mov_spd': 3088, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 750, ...}, 'abilities': [False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, ...], 'attack_range': 7000, 'attack_target': 0, 'kill_income': 46, 'camp_visible': [False, True], 'sight_area': 8500, 'buff_state': {}}
        #野怪?
        #{'config_id': 6827, 'runtime_id': 39, 'actor_type': 'ACTOR_MONSTER', 'sub_type': 'ACTOR_SUB_NONE', 'camp': 'PLAYERCAMP_MID', 'behav_mode': 'State_Idle', 'location': {'x': -4643, 'y': 48, 'z': 6583}, 'forward': {'x': 1000, 'y': 0, 'z': -11}, 'hp': 4000, 'max_hp': 4000, 'values': {'phy_atk': 140, 'phy_def': 0, 'mgc_atk': 140, 'mgc_def': 0, 'mov_spd': 2500, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 0, ...}, 'abilities': [False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, ...], 'attack_range': 100, 'attack_target': 0, 'kill_income': 90, 'camp_visible': [True, True], 'sight_area': 5000, 'buff_state': {}}

        main_camp_hero_vector_feature = self.process_hero_feature(frame_state)
        organ_feature = self.process_organ_feature(frame_state)
        soldier_feature = self.process_soldier_feature(frame_state)
        global_feature = self.global_feature_process.process_global_feature(frame_state)
        #for i in range(len(frame_state['npc_states'])):
        #    if frame_state['npc_states'][i]['config_id'] not in [1114,1112,1111,1113,44,46]:
        #        pass
        #atk_range 174 169 8000 173 7000
        feature = main_camp_hero_vector_feature + organ_feature + soldier_feature + global_feature
        #print(observation['frame_state']['hero_states'][0]['actor_state']['buff_state'])
        #print(observation['frame_state']['hero_states'][1]['actor_state']['buff_state'])
        #frame_state['frame_action']['dead_action']
        #{'death': {'config_id': 6803, 'runtime_id': 21, 'actor_type': 'ACTOR_MONSTER', 'sub_type': 'ACTOR_SUB_SOLDIER', 'camp': 'PLAYERCAMP_2', 'income_info': {...}, 'achievement_info': {...}, 'single_hurt_list': [...]}, 'killer': {'config_id': 169, 'runtime_id': 8, 'actor_type': 'ACTOR_HERO', 'sub_type': 'ACTOR_SUB_NONE', 'camp': 'PLAYERCAMP_1', 'hurt_info': [...], 'income_info': {...}, 'achievement_info': {...}}}
        #if len(frame_state['frame_action']) > 0:
        #    pass
        return feature


