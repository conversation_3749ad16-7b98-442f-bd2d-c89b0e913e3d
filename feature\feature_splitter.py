#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
Feature Splitter - 參考PPO的特徵切分方式處理153維特徵
"""

import torch
import numpy as np
from agent_ppo.conf.conf import DimConfig


class FeatureSplitter:
    """
    基於PPO風格的特徵切分器，將153維特徵切分成不同的組件
    """
    
    def __init__(self):
        # 基於您現有的153維特徵結構進行切分
        # 根據註釋：121(敵方英雄+攻擊範圍+英雄ID) + 7(我方防禦塔) + 1(全局特徵) + 24(死亡事件one hot) = 153
        
        # 特徵切分維度配置
        self.enemy_hero_dim = 121  # 敵方英雄+攻擊範圍+英雄ID
        self.friendly_organ_dim = 7   # 我方防禦塔
        self.global_feature_dim = 1   # 全局特徵
        self.death_event_dim = 24     # 死亡事件one hot
        
        # 驗證總維度
        total_dim = self.enemy_hero_dim + self.friendly_organ_dim + self.global_feature_dim + self.death_event_dim
        assert total_dim == 153, f"Feature dimensions don't match: {total_dim} != 153"
        
        # 切分點
        self.split_points = [
            self.enemy_hero_dim,
            self.friendly_organ_dim, 
            self.global_feature_dim,
            self.death_event_dim
        ]
        
    def split_features(self, feature_vec):
        """
        將153維特徵向量切分成不同的組件
        
        Args:
            feature_vec: torch.Tensor, shape [batch_size, 153]
            
        Returns:
            dict: 包含切分後的特徵組件
        """
        if isinstance(feature_vec, np.ndarray):
            feature_vec = torch.from_numpy(feature_vec)
            
        # 確保輸入維度正確
        if feature_vec.dim() == 1:
            feature_vec = feature_vec.unsqueeze(0)
            
        assert feature_vec.size(-1) == 153, f"Expected 153 features, got {feature_vec.size(-1)}"
        
        # 切分特徵
        feature_splits = feature_vec.split(self.split_points, dim=-1)
        
        return {
            'enemy_hero': feature_splits[0],      # [batch_size, 121] 敵方英雄+攻擊範圍+英雄ID
            'friendly_organ': feature_splits[1],  # [batch_size, 7]   我方防禦塔
            'global_info': feature_splits[2],     # [batch_size, 1]   全局特徵
            'death_events': feature_splits[3]     # [batch_size, 24]  死亡事件one hot
        }
        
    def process_enemy_hero_features(self, enemy_hero_vec):
        """
        處理敵方英雄特徵 (121維)
        參考PPO中對英雄特徵的處理方式
        """
        # 可以進一步細分敵方英雄特徵
        # 假設結構：英雄基本信息 + 攻擊範圍 + 英雄ID
        # 這裡可以根據實際需求進一步切分
        return enemy_hero_vec
        
    def process_friendly_organ_features(self, organ_vec):
        """
        處理我方建築物特徵 (7維)
        參考PPO中對建築物特徵的處理方式
        """
        return organ_vec
        
    def process_global_features(self, global_vec):
        """
        處理全局特徵 (1維)
        """
        return global_vec
        
    def process_death_events(self, death_vec):
        """
        處理死亡事件特徵 (24維 one hot)
        """
        return death_vec
        
    def get_processed_features(self, feature_vec):
        """
        獲取處理後的特徵，類似PPO的處理方式
        
        Args:
            feature_vec: torch.Tensor, shape [batch_size, 153]
            
        Returns:
            dict: 處理後的特徵組件
        """
        # 切分特徵
        split_features = self.split_features(feature_vec)
        
        # 處理各個組件
        processed_features = {
            'enemy_hero': self.process_enemy_hero_features(split_features['enemy_hero']),
            'friendly_organ': self.process_friendly_organ_features(split_features['friendly_organ']),
            'global_info': self.process_global_features(split_features['global_info']),
            'death_events': self.process_death_events(split_features['death_events'])
        }
        
        return processed_features
        
    def concat_features(self, processed_features):
        """
        將處理後的特徵重新拼接，類似PPO中的concat操作
        
        Args:
            processed_features: dict, 處理後的特徵組件
            
        Returns:
            torch.Tensor: 拼接後的特徵向量
        """
        feature_list = [
            processed_features['enemy_hero'],
            processed_features['friendly_organ'], 
            processed_features['global_info'],
            processed_features['death_events']
        ]
        
        return torch.cat(feature_list, dim=-1)


class PPOStyleFeatureProcessor:
    """
    PPO風格的特徵處理器，整合特徵切分和處理
    """
    
    def __init__(self):
        self.feature_splitter = FeatureSplitter()
        
    def forward(self, feature_vec):
        """
        前向處理，類似PPO模型中的特徵處理流程
        
        Args:
            feature_vec: torch.Tensor, shape [batch_size, 153]
            
        Returns:
            torch.Tensor: 處理後的特徵向量
        """
        # 1. 切分特徵
        processed_features = self.feature_splitter.get_processed_features(feature_vec)
        
        # 2. 可以在這裡添加更多的處理邏輯，類似PPO中的MLP處理
        # 例如：對不同類型的特徵應用不同的神經網絡
        
        # 3. 重新拼接特徵
        output_features = self.feature_splitter.concat_features(processed_features)
        
        return output_features, processed_features


def create_feature_processor():
    """
    創建特徵處理器的工廠函數
    """
    return PPOStyleFeatureProcessor()


if __name__ == "__main__":
    # 測試代碼
    processor = create_feature_processor()
    
    # 創建測試數據
    batch_size = 4
    test_features = torch.randn(batch_size, 153)
    
    # 處理特徵
    output_features, processed_components = processor.forward(test_features)
    
    print(f"Input shape: {test_features.shape}")
    print(f"Output shape: {output_features.shape}")
    print("Processed components shapes:")
    for key, value in processed_components.items():
        print(f"  {key}: {value.shape}")
